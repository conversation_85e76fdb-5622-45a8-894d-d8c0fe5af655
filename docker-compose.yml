version: "3.8"
services:
  api:
    image: ${IMAGE_NAME}
    #build:
      #context: .
      #dockerfile: FlairLoop.Api/Dockerfile
    container_name: flairloop_dotnet_api
    ports:
      - "8080:80"
      #- "8443:443"
    environment:
      ASPNETCORE_ENVIRONMENT: ${ENVIRONMENT}
      ASPNETCORE_HTTPS_PORT: 443
      ASPNETCORE_URLS: "http://+:80"
      ConnectionStrings__Database: ${CONN_DATABASE}
      #ASPNETCORE_Kestrel__Certificates__Default__Path: ${Kestrel__Certificates__Default__Path}
      #ASPNETCORE_Kestrel__Certificates__Default__Password: ${Kestrel__Certificates__Default__Password}
      Jwt__Secret: ${JWT_SECRET}
      Admin__Email: ${ADMIN_EMAIL}
      Admin__Password: ${ADMIN_PASSWORD}
      Email__SmtpServer: ${EMAIL_SMTP_SERVER}
      Email__SmtpPort: ${EMAIL_SMTP_PORT}
      Email__SmtpUsername: ${EMAIL_SMTP_USERNAME}
      Email__SmtpPassword: ${EMAIL_SMTP_PASSWORD}
    depends_on:
      - postgres
    volumes:
      - ${CERT_HOST_PATH}:/app/https/
    networks:
      - app_network

  postgres:
    image: postgres:15
    container_name: flairloop_postgres_db
    environment:
      POSTGRES_USER: flairloop_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: flairloop_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U flairloop_user "]
      interval: 300s
      timeout: 5s
      retries: 5
    networks:
      - app_network

volumes:
  postgres_data:

networks:
  app_network:
    driver: bridge
